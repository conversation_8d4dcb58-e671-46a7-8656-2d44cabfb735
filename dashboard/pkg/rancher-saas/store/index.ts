import { CoreStoreSpecifics, CoreStoreConfig } from '@shell/core/types';
import getters from './getters';
import mutations from './mutations';
import actions from './actions';

import { PRODUCT_NAME } from '../config';

const storeFactory = (): CoreStoreSpecifics => {
  return {
    state() {
      return {
        chat: {
          messages: [], isLoading: false, chatId: ''
        },
        expiration: Date.parse("0"),
        config: {
          isPLGMode: true
        }
      };
    },
    getters,
    mutations,
    actions
  };
};

const config: CoreStoreConfig = { namespace: PRODUCT_NAME };

export default {
  specifics: storeFactory(),
  config
};
