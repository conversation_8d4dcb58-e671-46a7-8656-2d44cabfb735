import { ProxyUrl } from '../config';

function timestamp(): string {
  return new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

export default {
  async loadConfig({ commit, state }: any) {
    try {
      const res = await fetch(ProxyUrl("admin/config"));
      const config = await res.json();

      commit('SET_CONFIG', config);
    } catch (e) {
      console.error('Error fetching config', e);
    }
  },
  async sendUserMessage({ commit, state }: any, content: string) {
    if (content == 'RESET_CHAT') {
      commit('RESET_CHAT');
    } else {
      commit('ADD_MESSAGE', {
        role: 'user', content, timestamp: timestamp()
      });
      commit('SET_LOADING', true);

      const chatId = state.chat.chatId;

      const currentMesage = content;

      await new Promise((r) => setTimeout(r, 2000));
      try {
        const res = await fetch(ProxyUrl("qindex/retrieve-generate"), {
          method:  'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...(chatId ? { 'X-Bedrock-Session': chatId } : {})
          },
          body: JSON.stringify({ query: currentMesage }),
        });

        if (chatId) {
          console.log('CHAT');
        } else {
          console.log('NO CHAT');
          commit('SET_CHAT_ID', res.headers.get('x-bedrock-session'));
        }

        const data = await res.json();

        const replyText = data.answer;
        // const replyText = message;
        const fullReply = `Rancher AI Assistant: ${ replyText }`;

        commit('ADD_MESSAGE', {
          role: 'assistant', content: fullReply, timestamp: timestamp()
        });
      } catch (e) {
        console.error(e);
        commit('ADD_MESSAGE', {
          role:      'assistant',
          content:   'Rancher AI Assistant: [Error contacting LLM]',
          timestamp: timestamp()
        });
      } finally {
        commit('SET_LOADING', false); // ✅ Hide loader
      }
    }
  },
  async getLatestBackup({ commit, state }: any, content: string) {

      await new Promise((r) => setTimeout(r, 2000));
      try {
        const res = await fetch(ProxyUrl("backup"), {
          method:  'GET',
        });

        const blob = await res.blob()

        console.log("Blob")
        console.log(blob)

      } catch (e) {
        console.log("EERRROORRRORORORORORO")
        console.error(e);
      }

  },

  async loginToQ({ commit,dispatch, state }: any, code: string) {

    const requestBody = JSON.stringify({ token: code })

    console.log("REQUEST BODY",requestBody,code)


    try {
      const res = await fetch(ProxyUrl("qindex/authenticate"), {
        method:  'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: code,
      });

      const blob = await res.json()

      console.log("Blob")
      console.log(blob)
      const expirationTime = Date.parse(blob)
      commit('SET_EXPIRATION',{expirationTime})

    } catch (e) {
      console.log("EERRROORRRORORORORORO")
      console.error(e);
    }

},
  initIntroMessage({ commit }: any) {
    commit('ADD_MESSAGE', {
      role:      'assistant',
      content:   'Rancher AI Assistant: Hello! How can I help you today?',
      timestamp: timestamp()
    });
  },
  setPLGMode({ commit }: any, isPLGMode: boolean) {
    commit('SET_PLG_MODE', isPLGMode);
  }
};
