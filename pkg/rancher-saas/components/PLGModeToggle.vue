<template>
  <div
    v-if="shouldShowToggle"
    class="plg-mode-toggle"
  >
    <div class="toggle-wrapper">
      <span class="toggle-label">Simple Mode</span>
      <ToggleSwitch
        :value="isPLGMode"
        :on-value="true"
        :off-value="false"
        on-label="Simple"
        off-label="Advanced"
        @update:value="updatePLGMode"
      />
      <span class="toggle-label">Advanced Mode</span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import ToggleSwitch from '@components/Form/ToggleSwitch/ToggleSwitch.vue';

export default defineComponent({
  name: 'PLGModeToggle',

  components: {
    ToggleSwitch,
  },

  data() {
    return {
      isPLGMode: true,
    };
  },

  computed: {
    shouldShowToggle() {
      // Only show toggle for EKS cluster creation/editing
      const routeName = String(this.$route.name || '');
      const isEKSRoute = routeName.includes('provisioning') &&
                        (this.$route.query?.type === 'amazoneks' ||
                         this.$route.params?.resource === 'provisioning.cattle.io.cluster');
      const isCreateOrEdit = routeName.includes('create') || routeName.includes('edit');

      return isEKSRoute && isCreateOrEdit;
    },
  },

  mounted() {
    // Get initial state from store if available
    this.isPLGMode = this.$store.getters['saasAdmin/isPLGMode'] ?? true;
  },

  watch: {
    // Watch store state to keep component in sync
    '$store.getters["saasAdmin/isPLGMode"]'(newVal: boolean) {
      this.isPLGMode = newVal;
    }
  },

  methods: {
    updatePLGMode(isPlgMode: boolean) {
      // Update store - this will trigger watchers in other components
      this.$store.dispatch('saasAdmin/setPLGMode', isPlgMode);
    },
  },
});
</script>

<style lang="scss" scoped>
.plg-mode-toggle {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;

  .toggle-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    background: var(--box-bg);
    border: 1px solid var(--border);
    border-radius: 4px;

    .toggle-label {
      font-size: 14px;
      color: var(--body-text);
      font-weight: 500;
    }
  }
}
</style>
